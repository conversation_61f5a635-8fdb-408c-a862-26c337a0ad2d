import 'package:espot/models/event_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/shared/snackbar.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:espot/ui/widgets/buttons.dart';
import 'package:espot/ui/widgets/forms.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:uuid/uuid.dart';

class DataEventInputPage extends StatefulWidget {
  final EventModel? data;
  const DataEventInputPage({
    this.data,
    super.key,
  });

  @override
  State<DataEventInputPage> createState() => _DataEventInputPageState();
}

class _DataEventInputPageState extends State<DataEventInputPage>
    with CacheManager {
  final descController = TextEditingController(text: '');
  final imageUrlController = TextEditingController(text: '');

  @override
  void initState() {
    super.initState();

    // Populate fields if editing an existing event
    if (widget.data != null) {
      descController.text = widget.data!.desc ?? '';
      imageUrlController.text = widget.data!.image ?? '';
    }
  }

  bool validate() {
    if (descController.text.isEmpty) {
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: Text(
            widget.data != null ? 'Update Events' : 'Input Events',
          ),
        ),
        body: ListView(
          padding: const EdgeInsets.symmetric(
            horizontal: 24,
          ),
          children: [
            const SizedBox(
              height: 30,
            ),
            Container(
              padding: const EdgeInsets.all(22),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: whiteColor,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CustomFormField(
                    title: 'Image Url',
                    controller: imageUrlController,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  CustomFormField(
                    title: 'Description',
                    maxLine: 5,
                    controller: descController,
                  ),
                  const SizedBox(
                    height: 30,
                  ),
                  CustomFilledButton(
                    title: 'Continue',
                    onPressed: () async {
                      if (validate()) {
                        // Show loading indicator
                        EasyLoading.show(status: 'Loading...');

                        try {
                          // Get database reference
                          final DatabaseReference dbRef =
                              FirebaseDatabase.instance.ref();

                          // CREATE new event
                          if (widget.data == null) {
                            // Generate new UUID for the event
                            final String eventId = const Uuid().v4();

                            // Create event data map
                            final Map<String, dynamic> eventData = {
                              'desc': descController.text,
                              'image': imageUrlController.text,
                            };

                            // Save to Firebase
                            await dbRef
                                .child('events')
                                .child(eventId)
                                .set(eventData);

                            // Success, navigate back
                            EasyLoading.dismiss();
                            CustomSnackBar.showToast(
                                context, 'Event created successfully');
                            Navigator.pushNamed(context, '/data-success');
                          }
                          // UPDATE existing event
                          else {
                            // Update event data map
                            final Map<String, dynamic> eventData = {
                              'desc': descController.text,
                              'image': imageUrlController.text,
                            };

                            // Update in Firebase
                            await dbRef
                                .child('events')
                                .child(widget.data!.uid!)
                                .update(eventData);

                            // Success, navigate back
                            EasyLoading.dismiss();
                            CustomSnackBar.showToast(
                                context, 'Event updated successfully');
                            Navigator.pushNamed(
                                context, '/data-success-update');
                          }
                        } catch (e) {
                          // Error handling
                          EasyLoading.dismiss();
                          CustomSnackBar.showToast(
                              context, 'Error: ${e.toString()}');
                        }
                      } else {
                        CustomSnackBar.showToast(
                            context, 'Inputan masih kosong');
                      }
                    },
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 30,
            )
          ],
        ));
  }
}
