import 'package:espot/models/event_model.dart';
import 'package:espot/shared/cache_manager.dart';
import 'package:espot/ui/pages/data_events_input_page.dart';
import 'package:espot/ui/widgets/data_event_item.dart';
import 'package:flutter/material.dart';
import 'package:espot/shared/theme.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:espot/shared/snackbar.dart';

class DataEventPage extends StatefulWidget with CacheManager {
  const DataEventPage({super.key});

  @override
  State<DataEventPage> createState() => _DataEventPageState();
}

class _DataEventPageState extends State<DataEventPage> {
  final searchController = TextEditingController(text: '');
  final DatabaseReference _dbRef =
      FirebaseDatabase.instance.ref().child('events');

  EventModel? selectedEvents;
  String searchResult = '';
  List<EventModel> eventsList = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    // Load events data from Firebase
    fetchEvents();
  }

  // Fetch events data from Firebase
  void fetchEvents() async {
    setState(() {
      isLoading = true;
    });

    try {
      // Get snapshot from Firebase
      final DataSnapshot snapshot = await _dbRef.get();

      if (snapshot.exists) {
        eventsList.clear();
        final data = snapshot.value as Map<dynamic, dynamic>;
        data.forEach((key, value) {
          final EventModel event = EventModel.fromMap(value, key);
          eventsList.add(event);
        });
      }
    } catch (e) {
      CustomSnackBar.showToast(
          context, 'Error fetching events: ${e.toString()}');
    } finally {
      setState(() {
        isLoading = false;
      });
    }
  }

  // Delete event
  void deleteEvent() async {
    if (selectedEvents == null || selectedEvents!.uid == null) {
      return;
    }

    try {
      // Delete from Firebase
      await _dbRef.child(selectedEvents!.uid!).remove();

      // Remove from list and reset selection
      setState(() {
        eventsList.removeWhere((event) => event.uid == selectedEvents!.uid);
        selectedEvents = null;
      });

      // Show success message
      Navigator.pushNamed(context, '/data-success-delete');
    } catch (e) {
      CustomSnackBar.showToast(
          context, 'Error deleting event: ${e.toString()}');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Events',
        ),
        actions: [
          IconButton(
            onPressed: () {
              Navigator.pushNamed(context, '/event-input');
            },
            icon: const Icon(Icons.add),
            iconSize: 30,
          ),
        ],
        leading: IconButton(
          onPressed: () {
            Navigator.pushNamedAndRemoveUntil(
                context, '/home', (route) => false);
          },
          icon: const Icon(Icons.arrow_back),
          iconSize: 30,
        ),
      ),
      body: isLoading
          ? const Center(child: CircularProgressIndicator())
          : ListView(
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
              ),
              children: [
                const SizedBox(
                  height: 40,
                ),
                Row(
                  children: [
                    Text(
                      'List Events',
                      style: blackTextStyle.copyWith(
                        fontSize: 16,
                        fontWeight: semiBold,
                      ),
                    ),
                    const Spacer(),
                    selectedEvents != null
                        ? Row(
                            children: [
                              GestureDetector(
                                child: const Icon(Icons.edit),
                                onTapUp: (details) {
                                  Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                      builder: (context) => DataEventInputPage(
                                        data: selectedEvents!,
                                      ),
                                    ),
                                  ).then((_) => fetchEvents());
                                },
                              ),
                              const SizedBox(
                                width: 20,
                              ),
                              GestureDetector(
                                child: const Icon(Icons.delete),
                                onTapUp: (details) {
                                  EasyLoading.show(status: 'loading...');
                                  deleteEvent();
                                  EasyLoading.dismiss();
                                },
                              ),
                            ],
                          )
                        : Container()
                  ],
                ),
                const SizedBox(
                  height: 15,
                ),
                eventsList.isEmpty
                    ? Center(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 50.0),
                          child: Text(
                            'No events found',
                            style: blackTextStyle.copyWith(
                              fontSize: 16,
                              fontWeight: medium,
                            ),
                          ),
                        ),
                      )
                    : ListView.builder(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: eventsList.length,
                        itemBuilder: (context, index) {
                          EventModel dataEvents = eventsList[index];
                          return GestureDetector(
                            onTap: () {
                              setState(() {
                                selectedEvents = dataEvents;
                              });
                            },
                            child: DataEventItem(
                              dataEvent: dataEvents,
                              isSelected: selectedEvents != null
                                  ? selectedEvents!.uid == dataEvents.uid
                                  : false,
                            ),
                          );
                        },
                      ),
                const SizedBox(
                  height: 10,
                ),
              ],
            ),
    );
  }
}
